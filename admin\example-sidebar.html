<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitBot Admin - Sidebar Example</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/navside.css">
    
    <style>
        /* Main content area styling */
        .main-content {
            padding: 2rem;
            min-height: calc(100vh - var(--navbar-height));
        }
        
        .content-header {
            margin-bottom: 2rem;
        }
        
        .content-title {
            font-size: 2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .content-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
        }
        
        .content-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .feature-item {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-md);
            padding: 1.5rem;
            text-align: center;
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }
        
        .feature-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .feature-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        /* Demo buttons */
        .demo-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius-md);
            background: var(--primary-color);
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
        }
        
        .demo-btn:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }
        
        .demo-btn.secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }
        
        .demo-btn.secondary:hover {
            background: var(--bg-accent);
        }
    </style>
</head>
<body class="light-theme">
    <!-- Navbar Container -->
    <div id="navbar"></div>
    
    <!-- Main Content -->
    <main class="main-content">
        <div class="content-header">
            <h1 class="content-title">Welcome to BitBot Admin</h1>
            <p class="content-subtitle">Modern sidebar navigation with dark/light theme support</p>
        </div>
        
        <div class="content-card">
            <h2>Sidebar Features</h2>
            <p>This sidebar includes all the features shown in your design:</p>
            
            <div class="feature-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h3 class="feature-title">Dashboard</h3>
                    <p class="feature-description">Main dashboard with overview and analytics</p>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3 class="feature-title">Course Management</h3>
                    <p class="feature-description">Add, view, and manage courses with expandable menu</p>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <h3 class="feature-title">Syllabus</h3>
                    <p class="feature-description">Manage syllabus content and structure</p>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3 class="feature-title">Time Tables</h3>
                    <p class="feature-description">Create and manage class schedules</p>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h3 class="feature-title">Notices</h3>
                    <p class="feature-description">Publish and manage important announcements</p>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3 class="feature-title">Fee Structure</h3>
                    <p class="feature-description">Manage fee structures and payment information</p>
                </div>
            </div>
            
            <div class="demo-buttons">
                <button class="demo-btn" onclick="window.navbarManager.setActiveSidebarItem('course')">
                    Activate Course Section
                </button>
                <button class="demo-btn" onclick="window.navbarManager.expandSidebarSection('syllabus')">
                    Expand Syllabus
                </button>
                <button class="demo-btn secondary" onclick="window.navbarManager.toggleTheme()">
                    Toggle Theme
                </button>
            </div>
        </div>
        
        <div class="content-card">
            <h2>Responsive Design</h2>
            <p>The sidebar automatically adapts to different screen sizes:</p>
            <ul style="margin-top: 1rem; color: var(--text-secondary);">
                <li><strong>Desktop (>1024px):</strong> Full sidebar with text and icons</li>
                <li><strong>Tablet (768px-1024px):</strong> Collapsed sidebar with icons only</li>
                <li><strong>Mobile (<768px):</strong> Hidden sidebar with hamburger menu toggle</li>
            </ul>
        </div>
    </main>
    
    <!-- JavaScript -->
    <script src="js/navside.js"></script>
</body>
</html>
