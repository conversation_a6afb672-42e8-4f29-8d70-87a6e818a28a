/* ===== SIDEBAR & NAVBAR STYLES ===== */

/* ===== CSS VARIABLES FOR THEMING ===== */
:root {
    /* Light Theme Colors */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-accent: #e2e8f0;

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-tertiary: #94a3b8;
    --text-inverse: #ffffff;

    /* Border Colors */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e0;
    --border-dark: #94a3b8;

    /* Layout Specific */
    --navbar-height: 70px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;

    /* Component Colors */
    --navbar-bg: var(--bg-primary);
    --navbar-border: var(--border-light);
    --navbar-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    --sidebar-bg: var(--bg-primary);
    --sidebar-border: var(--border-light);
    --sidebar-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    --sidebar-item-hover: var(--bg-tertiary);
    --sidebar-item-active: var(--primary-color);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme Variables */
.dark-theme {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #9ca3af;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    --bg-accent: #475569;

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e0;
    --text-tertiary: #94a3b8;
    --text-inverse: #1e293b;

    --border-light: #334155;
    --border-medium: #475569;
    --border-dark: #64748b;

    --navbar-bg: var(--bg-primary);
    --navbar-border: var(--border-light);
    --navbar-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);

    --sidebar-bg: var(--bg-primary);
    --sidebar-border: var(--border-light);
    --sidebar-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
    --sidebar-item-hover: var(--bg-tertiary);
    --sidebar-item-active: var(--primary-color);
}

/* ===== BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color var(--transition-normal), color var(--transition-normal);
    padding-left: var(--sidebar-width);
    padding-top: var(--navbar-height);
}

/* ===== TOP NAVBAR STYLES ===== */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--navbar-height);
    background-color: var(--navbar-bg);
    border-bottom: 1px solid var(--navbar-border);
    box-shadow: var(--navbar-shadow);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.top-navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 var(--spacing-lg);
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-normal);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.mobile-menu-toggle:hover {
    background-color: var(--bg-accent);
    color: var(--text-primary);
}

/* ===== BRAND SECTION ===== */
.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-shrink: 0;
}

.brand-logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: 1.2rem;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.brand-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.brand-subtitle {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* ===== SIDEBAR STYLES ===== */
.sidebar {
    position: fixed;
    top: var(--navbar-height);
    left: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--navbar-height));
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--sidebar-border);
    box-shadow: var(--sidebar-shadow);
    z-index: 999;
    transition: all var(--transition-normal);
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar-container {
    padding: var(--spacing-lg) 0;
}

/* Sidebar Items */
.sidebar-item {
    margin: 0 var(--spacing-md) var(--spacing-xs);
    border-radius: var(--radius-md);
    overflow: hidden;
    transition: all var(--transition-fast);
}

.sidebar-item:not(.expandable) {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.sidebar-item:not(.expandable):hover {
    background-color: var(--sidebar-item-hover);
    color: var(--text-primary);
}

.sidebar-item:not(.expandable).active {
    background-color: var(--sidebar-item-active);
    color: var(--text-inverse);
}

.sidebar-item:not(.expandable).active .sidebar-icon {
    color: var(--text-inverse);
}

/* Expandable Sidebar Items */
.sidebar-item.expandable .sidebar-main {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    cursor: pointer;
    color: var(--text-secondary);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.sidebar-item.expandable .sidebar-main:hover {
    background-color: var(--sidebar-item-hover);
    color: var(--text-primary);
}

.sidebar-item.expandable.expanded .sidebar-main {
    background-color: var(--sidebar-item-hover);
    color: var(--text-primary);
}

/* Sidebar Icons */
.sidebar-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    color: var(--text-secondary);
    font-size: 1.1rem;
    transition: all var(--transition-fast);
}

/* Sidebar Text */
.sidebar-text {
    flex: 1;
    font-size: 0.95rem;
    font-weight: 500;
}

/* Expand Arrow */
.expand-arrow {
    font-size: 0.8rem;
    transition: transform var(--transition-fast);
    color: var(--text-tertiary);
}

.sidebar-item.expanded .expand-arrow {
    transform: rotate(180deg);
}

/* Sidebar Submenu */
.sidebar-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-normal);
    background-color: var(--bg-tertiary);
    margin: 0 var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.sidebar-item.expanded .sidebar-submenu {
    max-height: 300px;
    padding: var(--spacing-xs) 0;
}

.sidebar-subitem {
    display: block;
    padding: var(--spacing-sm) var(--spacing-xl);
    text-decoration: none;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 400;
    transition: all var(--transition-fast);
    border-left: 3px solid transparent;
}

.sidebar-subitem:hover {
    background-color: var(--bg-accent);
    color: var(--text-primary);
    border-left-color: var(--primary-color);
}

.sidebar-subitem.active {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    border-left-color: var(--primary-hover);
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* ===== NAVBAR ACTIONS ===== */
.navbar-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-shrink: 0;
}

/* ===== THEME TOGGLE ===== */
.theme-toggle {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-lg);
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.theme-toggle:hover {
    background-color: var(--bg-accent);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.theme-toggle i {
    font-size: 1rem;
    transition: all var(--transition-fast);
    position: absolute;
}

.light-theme .theme-toggle .light-icon {
    opacity: 1;
    transform: rotate(0deg);
}

.light-theme .theme-toggle .dark-icon {
    opacity: 0;
    transform: rotate(180deg);
}

.dark-theme .theme-toggle .light-icon {
    opacity: 0;
    transform: rotate(-180deg);
}

.dark-theme .theme-toggle .dark-icon {
    opacity: 1;
    transform: rotate(0deg);
}

/* ===== PROFILE DROPDOWN ===== */
.profile-dropdown {
    position: relative;
}

.profile-trigger {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--radius-lg);
    background-color: transparent;
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    justify-content: flex-start;
}

.profile-trigger:hover {
    background-color: var(--bg-tertiary);
}

.profile-trigger.active {
    background-color: var(--bg-tertiary);
}

.profile-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--secondary-color), var(--text-tertiary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: 0.875rem;
    flex-shrink: 0;
}

.profile-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
    flex: 1;
}

.profile-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.profile-role {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.dropdown-arrow {
    font-size: 0.75rem;
    color: var(--text-secondary);
    transition: transform var(--transition-fast);
    flex-shrink: 0;
}

.profile-trigger.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* ===== DROPDOWN MENU ===== */
.dropdown-menu {
    position: absolute;
    top: calc(100% + var(--spacing-xs));
    right: 0;
    min-width: 280px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: 1001;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.dropdown-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: 1.1rem;
    flex-shrink: 0;
}

.dropdown-info {
    display: flex;
    flex-direction: column;
    line-height: 1.3;
}

.dropdown-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.dropdown-email {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-light);
    margin: 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    text-decoration: none;
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-fast);
    border: none;
    background: none;
    width: 100%;
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: var(--bg-tertiary);
    color: var(--primary-color);
}

.dropdown-item.logout-item:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.dropdown-item i {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}



/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .top-navbar-container {
        margin-left: var(--sidebar-collapsed-width);
    }

    .sidebar {
        width: var(--sidebar-collapsed-width);
    }

    .sidebar-text {
        display: none;
    }

    .expand-arrow {
        display: none;
    }

    .sidebar-submenu {
        display: none;
    }

    body {
        padding-left: var(--sidebar-collapsed-width);
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .top-navbar-container {
        margin-left: 0;
        padding: 0 var(--spacing-md);
    }

    .sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
        z-index: 1001;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .sidebar-text {
        display: block;
    }

    .expand-arrow {
        display: block;
    }

    .sidebar-submenu {
        display: block;
    }

    body {
        padding-left: 0;
    }

    .brand-text {
        display: none;
    }

    .profile-info {
        display: none;
    }

    .dropdown-menu {
        min-width: 250px;
        right: -10px;
    }
}

@media (max-width: 480px) {
    .dropdown-menu {
        min-width: 220px;
        right: -20px;
    }

    .dropdown-header {
        padding: var(--spacing-md);
    }

    .dropdown-item {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .sidebar {
        width: 280px;
    }
}

/* ===== SCROLLBAR STYLING ===== */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

.sidebar::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--border-dark);
}
