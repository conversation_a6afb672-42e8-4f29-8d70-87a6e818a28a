<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navbar - BitBot Admin</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Navbar CSS -->
    <link rel="stylesheet" href="css/navside.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Poppins', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-content {
            padding: 100px 20px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-card {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            background: var(--primary-color);
            color: white;
            cursor: pointer;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
        }
        
        .test-btn.secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }
        
        .test-btn.secondary:hover {
            background: var(--border-light);
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }
        
        .status.error {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
    </style>
</head>
<body class="light-theme">
    <!-- Dynamic Navbar Container -->
    <div id="navbar"></div>
    
    <!-- Test Content -->
    <div class="test-content">
        <div class="test-card">
            <h1><i class="fas fa-vial"></i> Navbar & Sidebar Test Page</h1>
            <p>This page tests the new hamburger menu and sliding sidebar functionality.</p>
            
            <div id="status" class="status">
                <i class="fas fa-spinner fa-spin"></i> Loading navbar...
            </div>
            
            <h3>Test Instructions:</h3>
            <ol>
                <li><strong>Hamburger Menu:</strong> Click the 3-line icon in the top-left to open the sidebar</li>
                <li><strong>Sidebar Navigation:</strong> Click on menu items to navigate</li>
                <li><strong>Close Sidebar:</strong> Click the X button or click outside the sidebar</li>
                <li><strong>Theme Toggle:</strong> Click the sun/moon icon to switch themes</li>
                <li><strong>Responsive:</strong> Resize the window to test mobile behavior</li>
            </ol>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="testToggleSidebar()">
                    <i class="fas fa-bars"></i> Toggle Sidebar
                </button>
                <button class="test-btn" onclick="testToggleTheme()">
                    <i class="fas fa-palette"></i> Toggle Theme
                </button>
                <button class="test-btn secondary" onclick="testConsoleLog()">
                    <i class="fas fa-terminal"></i> Check Console
                </button>
            </div>
        </div>
        
        <div class="test-card">
            <h3><i class="fas fa-info-circle"></i> Expected Behavior</h3>
            <ul>
                <li>✅ Navbar should appear at the top with hamburger menu, logo, and theme toggle</li>
                <li>✅ Sidebar should be hidden by default</li>
                <li>✅ Clicking hamburger should slide sidebar in from the left</li>
                <li>✅ Sidebar should have expandable menu sections</li>
                <li>✅ Theme toggle should switch between light and dark modes</li>
                <li>✅ All animations should be smooth</li>
            </ul>
        </div>
    </div>
    
    <!-- Navbar JavaScript -->
    <script src="js/navside.js"></script>
    <script>
        let navbarManager;
        
        // Test functions
        function testToggleSidebar() {
            if (navbarManager) {
                navbarManager.toggleSidebar();
            } else {
                alert('NavbarManager not initialized yet');
            }
        }
        
        function testToggleTheme() {
            if (navbarManager) {
                navbarManager.toggleTheme();
            } else {
                alert('NavbarManager not initialized yet');
            }
        }
        
        function testConsoleLog() {
            console.log('=== NAVBAR TEST DEBUG INFO ===');
            console.log('NavbarManager instance:', navbarManager);
            console.log('Navbar loaded:', navbarManager?.navbarLoaded);
            console.log('Sidebar visible:', navbarManager?.sidebarVisible);
            console.log('Current theme:', document.body.className);
            console.log('Hamburger element:', document.getElementById('hamburgerToggle'));
            console.log('Sidebar element:', document.getElementById('sidebar'));
            alert('Check the browser console (F12) for debug information');
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('Test page DOM loaded');
            
            try {
                // Initialize navbar manager
                if (typeof NavbarManager !== 'undefined') {
                    navbarManager = new NavbarManager();
                    console.log('NavbarManager initialized successfully');
                    
                    // Wait for navbar to load
                    setTimeout(() => {
                        const statusEl = document.getElementById('status');
                        if (navbarManager.navbarLoaded) {
                            statusEl.className = 'status success';
                            statusEl.innerHTML = '<i class="fas fa-check"></i> Navbar loaded successfully! Try the hamburger menu.';
                        } else {
                            statusEl.className = 'status error';
                            statusEl.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Navbar failed to load. Check console for errors.';
                        }
                    }, 1000);
                    
                } else {
                    throw new Error('NavbarManager class not found');
                }
            } catch (error) {
                console.error('Error initializing navbar:', error);
                const statusEl = document.getElementById('status');
                statusEl.className = 'status error';
                statusEl.innerHTML = '<i class="fas fa-times"></i> Error: ' + error.message;
            }
        });
    </script>
</body>
</html>
