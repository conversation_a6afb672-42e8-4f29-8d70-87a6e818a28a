# BitBot Admin Sidebar Component

A modern, responsive sidebar component with dark/light theme support for the BitBot Admin panel.

## Features

- ✨ **Modern Design**: Clean, professional interface matching the provided design
- 🌓 **Dark/Light Theme**: Automatic theme switching with smooth transitions
- 📱 **Mobile Responsive**: Collapsible sidebar that becomes an overlay on mobile
- 🎯 **Interactive Dropdowns**: Expandable menu items with smooth animations
- 💾 **State Persistence**: Remembers collapsed state and theme preference
- 🔧 **Easy Integration**: Simple HTML structure with automatic loading
- ♿ **Accessible**: Keyboard navigation and screen reader friendly
- 🎨 **Customizable**: CSS variables for easy theming

## Files Structure

```
admin/
├── sidebar.html          # Sidebar HTML structure
├── css/navside.css       # Styles for both navbar and sidebar
├── js/navside.js         # JavaScript functionality
└── sidebar-example.html  # Example implementation
```

## Quick Start

1. **Include the required files in your HTML:**

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Sidebar CSS -->
    <link rel="stylesheet" href="css/navside.css">
</head>
<body class="light-theme">
    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>
    
    <!-- Your main content -->
    <main class="main-content">
        <!-- Your page content here -->
    </main>
    
    <!-- Sidebar JavaScript -->
    <script src="js/navside.js"></script>
</body>
</html>
```

2. **Add CSS for your main content:**

```css
.main-content {
    margin-left: 280px; /* Sidebar width */
    padding: 2rem;
    transition: margin-left 0.3s ease-in-out;
}

.sidebar.collapsed + .main-content {
    margin-left: 70px; /* Collapsed sidebar width */
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0; /* No margin on mobile */
    }
}
```

## Navigation Structure

The sidebar includes the following main sections:

- **Dashboard** - Main overview page
- **Course** - Course management with submenus
- **Syllabus** - Syllabus management with submenus  
- **Time Table** - Schedule management with submenus
- **Notice** - Notice management with submenus
- **Fee Structure** - Fee management with submenus
- **Faculty** - Faculty management with submenus
- **College Info** - College information with submenus
- **Student** - Student management with submenus

## JavaScript API

### Events

Listen to navigation and sidebar events:

```javascript
// Navigation events
document.addEventListener('navbar-navigate', (e) => {
    console.log('Navigated to:', e.detail.section, e.detail.page);
});

// Sidebar toggle events
document.addEventListener('sidebar-toggle', (e) => {
    console.log('Sidebar collapsed:', e.detail.collapsed);
});
```

### Methods

Access the sidebar manager instance:

```javascript
// Get current theme
const theme = window.navbarManager.getCurrentTheme();

// Set active navigation item
window.navbarManager.setActiveNavItem('#dashboard');

// Set active submenu item
window.navbarManager.setActiveSubmenuItem('#add-course');

// Get sidebar state
const state = window.navbarManager.getSidebarState();

// Programmatically collapse/expand sidebar
window.navbarManager.setSidebarCollapsed(true);
```

## Theme Support

The component supports both light and dark themes:

```javascript
// Toggle theme programmatically
window.navbarManager.toggleTheme();

// Set specific theme
window.navbarManager.setTheme('dark'); // or 'light'
```

Add theme toggle button:

```html
<button class="theme-toggle" id="themeToggle">
    <i class="fas fa-sun light-icon"></i>
    <i class="fas fa-moon dark-icon"></i>
</button>
```

## Customization

### CSS Variables

Customize colors and spacing by modifying CSS variables:

```css
:root {
    --primary-color: #3b82f6;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    /* ... other variables */
}
```

### Adding New Menu Items

Add new navigation items to `sidebar.html`:

```html
<div class="nav-item">
    <a href="#new-section" class="nav-link" data-page="new-section">
        <i class="fas fa-new-icon nav-icon"></i>
        <span class="nav-text">New Section</span>
        <span class="nav-tooltip">New Section</span>
    </a>
</div>
```

## Mobile Behavior

- Sidebar becomes an overlay on screens ≤ 768px
- Automatic close after navigation on mobile
- Touch-friendly interactions
- Overlay background for better UX

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Dependencies

- Font Awesome 6.4.0+ (for icons)
- Google Fonts Poppins (for typography)
- Modern browser with CSS Grid and Flexbox support

## Example Implementation

See `sidebar-example.html` for a complete working example with:
- Proper HTML structure
- CSS integration
- JavaScript event handling
- Theme switching
- Responsive behavior

## Troubleshooting

**Sidebar not loading:**
- Ensure `sidebar.html` is in the same directory
- Check browser console for fetch errors
- Verify all CSS and JS files are properly linked

**Theme not switching:**
- Make sure body has `light-theme` or `dark-theme` class
- Check if theme toggle button has correct ID
- Verify localStorage is available

**Mobile overlay not working:**
- Ensure viewport meta tag is present
- Check CSS media queries are loading
- Verify touch events are not blocked
